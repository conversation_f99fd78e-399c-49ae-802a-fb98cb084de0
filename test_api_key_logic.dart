import 'package:flutter/foundation.dart';
import 'lib/services/api_key_manager.dart';
import 'lib/services/secure_storage_service.dart';
import 'lib/models/ai_provider_settings.dart';

/// 测试重构后的API key逻辑
Future<void> testApiKeyLogic() async {
  debugPrint('=== 测试重构后的API key逻辑 ===');
  
  try {
    final apiKeyManager = APIKeyManager();
    final testProviderId = 'test_openrouter';
    final testApiKey = 'sk_test_1234567890123456789012345678901234567890';
    
    // 1. 测试保存API密钥
    debugPrint('1. 测试保存API密钥...');
    await apiKeyManager.saveProviderApiKey(testProviderId, testApiKey);
    
    // 2. 测试获取API密钥
    debugPrint('2. 测试获取API密钥...');
    final retrievedKey = await apiKeyManager.getProviderApiKey(testProviderId);
    debugPrint('获取的密钥: ${retrievedKey.substring(0, 10)}...');
    
    // 3. 测试验证API密钥
    debugPrint('3. 测试验证API密钥...');
    final isValid = await apiKeyManager.hasValidProviderApiKey(testProviderId);
    debugPrint('API密钥有效性: $isValid');
    
    // 4. 测试同步验证
    debugPrint('4. 测试同步验证...');
    final testProvider = AIProviderSettings(
      id: testProviderId,
      name: 'Test Provider',
      apiKey: testApiKey,
      apiUrl: 'https://test.com',
      model: 'test-model',
    );
    final isSyncValid = apiKeyManager.hasValidProviderApiKeySync(testProvider);
    debugPrint('同步验证结果: $isSyncValid');
    
    // 5. 测试格式验证
    debugPrint('5. 测试格式验证...');
    final isFormatValid = apiKeyManager.isValidApiKeyFormat(testApiKey);
    debugPrint('格式验证结果: $isFormatValid');
    
    // 6. 清理测试数据
    debugPrint('6. 清理测试数据...');
    await apiKeyManager.removeProviderApiKey(testProviderId);
    
    debugPrint('=== API key逻辑测试完成 ===');
    
  } catch (e) {
    debugPrint('测试失败: $e');
  }
}

void main() async {
  await testApiKeyLogic();
}
